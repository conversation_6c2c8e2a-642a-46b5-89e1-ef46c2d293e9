import os
import wandb
import torch
import pandas as pd
import numpy as np
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.metrics import accuracy_score, classification_report
from transformers import BertTokenizer, BertForSequenceClassification, Trainer, TrainingArguments, pipeline
from nltk.sentiment.vader import SentimentIntensityAnalyzer
from sklearn.feature_extraction.text import TfidfVectorizer
from datasets import Dataset

# Set up environment variable for W&B (Optional)
os.environ["WANDB_MODE"] = "offline"  # Change to "online" if you want to sync results to the cloud
# If you don't want W&B tracking, uncomment the following line
# os.environ["WANDB_DISABLED"] = "true"

# Download VADER lexicon
import nltk
nltk.download('vader_lexicon')

# Step 1: Load Chrome Data from MongoDB
from pymongo import MongoClient

client = MongoClient("mongodb+srv://karthikroshan3456:<EMAIL>/mydatabase?retryWrites=true&w=majority")  # Replace with your MongoDB URL
db = client["SmartMindWatch"]
collection = db["searchhistories"]
chrome_data = pd.DataFrame(list(collection.find({}, {"_id": 0})))

# Step 2: Time Parsing Function (converts "10m 5s" into seconds)
import re

def parse_time(time_str):
    minutes = 0
    seconds = 0
    time_match = re.match(r"(\d+)m (\d+)s", time_str)
    if time_match:
        minutes = int(time_match.group(1))
        seconds = int(time_match.group(2))
    return minutes * 60 + seconds

chrome_data['Time_Spent'] = chrome_data['totalTimeSpent'].apply(parse_time)

# Step 3: Load labeled CSV for training
df = pd.read_csv("data1.csv", encoding='ISO-8859-1')
df.columns = [col.lower() for col in df.columns]
df = df.rename(columns={"class": "label"})

# Step 4: Tokenize using BERT
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')

def tokenize(example):
    return tokenizer(example["text"], truncation=True, padding="max_length")

dataset = Dataset.from_pandas(df)
tokenized_dataset = dataset.map(tokenize)

# Step 5: Train BERT for classification
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=3)

# W&B: Ensure you set a custom run_name to avoid conflicts with output_dir
training_args = TrainingArguments(
    output_dir="./results",
    run_name="bert_train_run",  # Custom run name to avoid conflict with output_dir
    num_train_epochs=2,
    per_device_train_batch_size=8,
    evaluation_strategy="no",
    save_strategy="no",
    logging_dir="./logs",
    learning_rate=2e-5,
    weight_decay=0.01,
)

trainer = Trainer(model=model, args=training_args, train_dataset=tokenized_dataset)
trainer.train()

# Step 6: Load as prediction pipeline
bert_classifier = pipeline("text-classification", model=model, tokenizer=tokenizer)

# Step 7: Sentiment Analysis with VADER
analyzer = SentimentIntensityAnalyzer()
chrome_data['Sentiment_Score'] = chrome_data['query'].apply(lambda x: analyzer.polarity_scores(x)['compound'])

# Step 8: TF-IDF on Chrome search queries
vectorizer = TfidfVectorizer(stop_words='english', max_features=100)
X_text = vectorizer.fit_transform(chrome_data['query']).toarray()

# Step 9: Extract BERT Probabilities
def extract_bert_probabilities(text):
    preds = bert_classifier(text)
    probs = [0, 0, 0]
    for p in preds:
        label_idx = int(p['label'].split('_')[-1])
        probs[label_idx] = p['score']
    return probs

bert_probs = chrome_data['query'].apply(extract_bert_probabilities)
bert_df = pd.DataFrame(bert_probs.tolist(), columns=['Prob_NoRisk', 'Prob_Suicide', 'Prob_Depression'])

# Step 10: Combine features
X_features = np.column_stack((
    X_text,
    chrome_data[['Time_Spent', 'Sentiment_Score']].to_numpy(),
    bert_df.to_numpy()
))

# Step 11: Train Random Forest
y = df['label'].values
X_train, X_test, y_train, y_test = train_test_split(X_features, y[:len(X_features)], test_size=0.2, random_state=42)

rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train, y_train)

# Evaluate
print("Model Accuracy:", accuracy_score(y_test, rf_model.predict(X_test)))
print("Classification Report:\n", classification_report(y_test, rf_model.predict(X_test)))

# Step 12: Predict on Chrome MongoDB Data
def predict_risk(text, time_spent):
    vader_score = analyzer.polarity_scores(text)['compound']
    tfidf_vector = vectorizer.transform([text]).toarray()
    bert_probs = extract_bert_probabilities(text)
    combined = np.column_stack((tfidf_vector, [[time_spent, vader_score] + bert_probs]))
    return rf_model.predict(combined)[0]

chrome_data['Predicted_Risk'] = chrome_data.apply(lambda row: predict_risk(row['query'], row['Time_Spent']), axis=1)

# Optional: Add label for readability
label_map = {0: "No Risk", 1: "Suicide", 2: "Depression"}
chrome_data['Risk_Label'] = chrome_data['Predicted_Risk'].map(label_map)

# Final Output
print(chrome_data[['query', 'Time_Spent', 'Risk_Label']])
