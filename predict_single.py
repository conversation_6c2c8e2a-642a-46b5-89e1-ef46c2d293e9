import joblib
import numpy as np
import os
import dotenv
from transformers import BertToken<PERSON>, BertForSequenceClassification, pipeline

# Load environment variables from .env file if it exists
dotenv.load_dotenv()

# Store API key in environment variable
API_KEY = os.getenv('API_KEY', '45430e6aea0b8a83f2d65fa376dbd54de606cb9a')  # Default value as fallback

def load_models():
    """Load all the trained models"""
    # Check if models exist
    if not os.path.exists('./models/rf_model.joblib'):
        print("Error: Models not found. Please run train.py first to train the models.")
        return None, None, None, None

    print("Loading models...")
    # Load models
    rf_model = joblib.load('./models/rf_model.joblib')
    vectorizer = joblib.load('./models/vectorizer.joblib')
    analyzer = joblib.load('./models/vader_analyzer.joblib')

    # Load BERT
    tokenizer = BertTokenizer.from_pretrained("./models/bert_model")
    bert_model = BertForSequenceClassification.from_pretrained("./models/bert_model")
    bert_classifier = pipeline("text-classification", model=bert_model, tokenizer=tokenizer)

    print("Models loaded successfully!")
    return rf_model, vectorizer, analyzer, bert_classifier

def extract_bert_probabilities(text, bert_classifier):
    """Extract probabilities from BERT classifier"""
    preds = bert_classifier(text)
    probs = [0, 0, 0]
    for p in preds:
        label_idx = int(p['label'].split('_')[-1])
        probs[label_idx] = p['score']
    return probs

def predict_risk(text, time_spent, rf_model, vectorizer, analyzer, bert_classifier):
    """Predict risk level for a single query"""
    # Get VADER sentiment score
    vader_score = analyzer.polarity_scores(text)['compound']

    # Get TF-IDF vector
    tfidf_vector = vectorizer.transform([text]).toarray()

    # Get BERT probabilities
    bert_probs = extract_bert_probabilities(text, bert_classifier)

    # Combine features
    features = np.column_stack((tfidf_vector, [[time_spent, vader_score] + bert_probs]))

    # Predict risk level
    risk_level = rf_model.predict(features)[0]

    # Map risk level to label
    label_map = {0: "No Risk", 1: "Suicide", 2: "Depression"}
    risk_label = label_map[risk_level]

    return risk_level, risk_label, {
        'vader_score': vader_score,
        'bert_probs': bert_probs
    }

if __name__ == "__main__":
    # Load models
    rf_model, vectorizer, analyzer, bert_classifier = load_models()

    if rf_model is None:
        exit(1)

    # Interactive prediction loop
    print("\n=== Mental Health Risk Prediction ===")
    print("Enter 'q' to quit")

    while True:
        # Get user input
        query = input("\nEnter search query: ")
        if query.lower() == 'q':
            break

        time_str = input("Enter time spent (e.g., '5m 30s'): ")

        # Parse time
        import re
        time_spent = 0
        match = re.match(r"(\d+)m (\d+)s", time_str)
        if match:
            time_spent = int(match.group(1)) * 60 + int(match.group(2))
        else:
            try:
                time_spent = int(time_str)
            except:
                print("Invalid time format. Using 0 seconds.")

        # Make prediction
        risk_level, risk_label, details = predict_risk(
            query, time_spent, rf_model, vectorizer, analyzer, bert_classifier
        )

        # Print results
        print("\nPrediction Results:")
        print(f"Query: {query}")
        print(f"Time Spent: {time_spent} seconds")
        print(f"Risk Level: {risk_label} (Level {risk_level})")
        print(f"Sentiment Score: {details['vader_score']:.4f}")
        print(f"BERT Probabilities: No Risk: {details['bert_probs'][0]:.4f}, " +
              f"Suicide: {details['bert_probs'][1]:.4f}, Depression: {details['bert_probs'][2]:.4f}")
