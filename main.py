import pandas as pd
import numpy as np
import torch
from transformers import Bert<PERSON>oken<PERSON>, BertForSequenceClassification, pipeline, Trainer, TrainingArguments
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from sklearn.feature_extraction.text import TfidfVectorizer
from datasets import Dataset

# Load the labeled CSV dataset with updated labels (2 = depression, 1 = suicide, 0 = no risk)
df = pd.read_csv("real_data.csv", encoding='ISO-8859-1')

# Ensure column names are lowercase for consistency
if 'text' not in df.columns:
    df.columns = [col.lower() for col in df.columns]

# Tokenization using BERT tokenizer
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')

def tokenize(example):
    # Check if text field exists and is not empty
    if "text" in example and example["text"] is not None and example["text"] != "":
        return tokenizer(example["text"], truncation=True, padding="max_length")
    else:
        # Return empty tokenization for missing text
        return tokenizer("[PAD]", truncation=True, padding="max_length")

# Clean the dataset - ensure no missing values
df = df.dropna(subset=['text', 'class'])

# Ensure all labels are integers
df['class'] = df['class'].astype(int)

# Convert to Hugging Face Dataset format
dataset = Dataset.from_pandas(df)
tokenized_dataset = dataset.map(tokenize)

# Add label column as tensor
# Rename the 'class' column to 'label' for compatibility with HuggingFace Trainer
tokenized_dataset = tokenized_dataset.rename_column("class", "label")


# Load BERT model for multi-class classification (3 classes: 0 = No Risk, 1 = Suicide, 2 = Depression)
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=3)

# Training arguments
training_args = TrainingArguments(
    output_dir="./results",
    num_train_epochs=3,
    per_device_train_batch_size=8,
    evaluation_strategy="no",
    save_strategy="no",
    logging_dir="./logs",
    learning_rate=2e-5,
    weight_decay=0.01,
)

# Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_dataset
)

# Train the model
trainer.train()

# Reload as a pipeline for prediction
bert_classifier = pipeline("text-classification", model=model, tokenizer=tokenizer)

# Load Chrome Data (Example DataFrame)
data = pd.DataFrame({
    'Search_Query': ["I want to end my life", "How to fight depression", "No one understands me", "Therapy near me"],
    'Website_Visited': ["suicideforum.com", "helpguide.org", "reddit.com/r/depression", "psychologytoday.com"],
    'Time_Spent': [15, 8, 10, 6]
})

# Sentiment Analysis using VADER
analyzer = SentimentIntensityAnalyzer()
data['Sentiment_Score'] = data['Search_Query'].apply(lambda x: analyzer.polarity_scores(x)['compound'])

# Keyword Extraction (TF-IDF)
vectorizer = TfidfVectorizer(stop_words='english', max_features=100)
X_text = vectorizer.fit_transform(data['Search_Query']).toarray()

# BERT Prediction Score for multi-class (score as depression risk signal)
def extract_bert_sentiment_score(text):
    result = bert_classifier(text)[0]
    label = result['label']
    score = result['score']
    if label == "LABEL_2":  # Depression
        return score
    elif label == "LABEL_1":  # Suicide
        return score * 0.7
    else:
        return -score

# Add BERT Score
data['BERT_Score'] = data['Search_Query'].apply(extract_bert_sentiment_score)

# Combine Features: TF-IDF + Time Spent + VADER Score + BERT Score
X_features = np.column_stack((X_text, data[['Time_Spent', 'Sentiment_Score', 'BERT_Score']]))

# Labels (Manually Annotated for Training)
y = np.array([1, 2, 2, 0])  # 0: No Risk, 1: Suicide, 2: Depression

# Split Data
X_train, X_test, y_train, y_test = train_test_split(X_features, y, test_size=0.2, random_state=42)

# Train RandomForest Model
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train, y_train)

# Predictions
y_pred = rf_model.predict(X_test)
print("Accuracy:", accuracy_score(y_test, y_pred))
print("Classification Report:\n", classification_report(y_test, y_pred))

# Predict Risk using Random Forest + BERT Logic for unseen query
def predict_risk(text, time_spent):
    vader_score = analyzer.polarity_scores(text)['compound']
    tfidf_vector = vectorizer.transform([text]).toarray()
    bert_score = extract_bert_sentiment_score(text)

    combined_features = np.column_stack((tfidf_vector, [[time_spent, vader_score, bert_score]]))
    risk_level = rf_model.predict(combined_features)[0]
    return risk_level

# Example Prediction
example_text = "I am depressed"
example_time = 12  # in minutes
prediction = predict_risk(example_text, example_time)
print("Predicted Risk Level:", prediction)
