# Core libraries
numpy
pandas
joblib
scikit-learn
torch

# NLP & Transformers
transformers
datasets

# Sentiment Analysis
vaderSentiment

# MongoDB client
pymongo

# Optional: suppress warnings
# warnings is part of the Python stdlib; no need to include it

# Optional: OS and regex libraries are also part of the stdlib
# os
# re
numpy==1.24.4
pandas==2.1.3
scikit-learn==1.3.0
torch==2.1.0
transformers==4.36.2
datasets==2.15.0
vaderSentiment==3.3.2
pymongo==4.6.1
joblib==1.3.2
