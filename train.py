import os
import warnings
import pandas as pd
import numpy as np
import joblib
import torch

from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.preprocessing import StandardScaler
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer

from transformers import (
    BertTokenizer, BertForSequenceClassification,
    Trainer, TrainingArguments, pipeline
)

from datasets import Dataset

# Setup and Environment
warnings.filterwarnings("ignore", category=FutureWarning)
os.environ["WANDB_DISABLED"] = "true"
os.makedirs("models", exist_ok=True)
os.makedirs("logs", exist_ok=True)

print("📦 Loading and preprocessing data...")

# Label Mapping
label_mapping = {

    "No_Risk": 0,
    "Depression": 1,
    "Suicide": 2,
    "Isolation": 3,
    "Anxiety": 4


}

reverse_label_mapping = {v: k for k, v in label_mapping.items()}
joblib.dump(reverse_label_mapping, './models/label_mapping.joblib')

# Load dataset
df = pd.read_csv("data1.csv", encoding='ISO-8859-1')
df.columns = [col.lower() for col in df.columns]

# Resolve label column
label_col = None
if 'label' in df.columns:
    label_col = 'label'
elif 'class' in df.columns:
    label_col = 'class'
else:
    raise ValueError("Dataset must contain either 'label' or 'class' column")

df = df.dropna(subset=['text', label_col])

# Convert string labels to integer codes
if df[label_col].dtype == object:
    df[label_col] = df[label_col].map(label_mapping)

# Tokenization using BERT
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')

def tokenize(example):
    return tokenizer(example["text"] or "[PAD]", truncation=True, padding="max_length")

dataset = Dataset.from_pandas(df)
tokenized_dataset = dataset.map(tokenize)

if 'class' in tokenized_dataset.column_names and 'label' not in tokenized_dataset.column_names:
    tokenized_dataset = tokenized_dataset.rename_column("class", "label")
elif 'label' in tokenized_dataset.column_names and 'class' in tokenized_dataset.column_names:
    tokenized_dataset = tokenized_dataset.remove_columns(["class"])

def convert_label_to_int(example):
    try:
        example['label'] = int(example['label'])
    except:
        example['label'] = 0
    return example

tokenized_dataset = tokenized_dataset.map(convert_label_to_int)

# Load model
print("🧠 Training BERT model...")
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=5)

training_args = TrainingArguments(
    output_dir="./models/bert_checkpoints",
    num_train_epochs=3,
    per_device_train_batch_size=8,
    save_strategy="no",
    logging_dir="./logs",
    learning_rate=2e-5,
    weight_decay=0.01,
    report_to="none"
)

trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_dataset
)

print("⏳ BERT training started...")
trainer.train()

# Save model and tokenizer
model.save_pretrained("./models/bert_model")
tokenizer.save_pretrained("./models/bert_model")

# TF-IDF + VADER + BERT PROBS
print("🔍 Training TF-IDF + RandomForest with Sentiment and BERT Probs...")

text_data = df['text'].tolist()
example_data = pd.DataFrame({'Search_Query': text_data, 'Time_Spent': [10] * len(text_data)})

# Sentiment Analysis
analyzer = SentimentIntensityAnalyzer()
example_data['Sentiment_Score'] = example_data['Search_Query'].apply(lambda x: analyzer.polarity_scores(x)['compound'])
joblib.dump(analyzer, './models/vader_analyzer.joblib')

# TF-IDF
vectorizer = TfidfVectorizer(stop_words='english', max_features=100)
X_text = vectorizer.fit_transform(example_data['Search_Query']).toarray()
joblib.dump(vectorizer, './models/vectorizer.joblib')

# Feature Scaling
scaler = StandardScaler()
scaled_values = scaler.fit_transform(example_data[['Time_Spent', 'Sentiment_Score']])
example_data['Time_Spent_Scaled'] = scaled_values[:, 0]
example_data['Sentiment_Score_Scaled'] = scaled_values[:, 1]
joblib.dump(scaler, './models/scaler.joblib')

# BERT Pipeline
bert_classifier = pipeline(
    "text-classification",
    model=model,
    tokenizer=tokenizer,
    truncation=True
)

def extract_bert_probabilities(text):
    try:
        preds = bert_classifier(text)
        num_labels = model.config.num_labels
        probs = [0] * num_labels
        for p in preds:
            label_idx = int(p['label'].split('_')[-1])
            probs[label_idx] = p['score']
        return probs
    except:
        return [0] * model.config.num_labels

example_data['BERT_Probs'] = example_data['Search_Query'].apply(extract_bert_probabilities)

# Feature engineering
X_features = []
for i, row in example_data.iterrows():
    features = np.concatenate((
        X_text[i],
        [row['Time_Spent_Scaled'], row['Sentiment_Score_Scaled']],
        row['BERT_Probs']
    ))
    X_features.append(features)

X = np.array(X_features)
y = df[label_col].values

# Train/Test Split
X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)

# Train Random Forest
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train, y_train)

# Evaluate
y_pred = rf_model.predict(X_test)
print("🎯 RandomForest Accuracy:", accuracy_score(y_test, y_pred))
print("📊 Classification Report:\n", classification_report(y_test, y_pred))

# Save Random Forest
joblib.dump(rf_model, './models/rf_model.joblib')

print("✅ All models trained and saved successfully.")
print("🔮 Use `predict.py` to make predictions with these models.")
