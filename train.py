import pandas as pd
import numpy as np
import torch
import joblib
import os
import dotenv
from transformers import Bert<PERSON>oken<PERSON>, BertForSequenceClassification, pipeline, Trainer, TrainingArguments
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, classification_report
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from sklearn.feature_extraction.text import TfidfVectorizer
from datasets import Dataset

# Load environment variables from .env file if it exists
dotenv.load_dotenv()

# Store API key in environment variable
API_KEY = os.getenv('API_KEY', '45430e6aea0b8a83f2d65fa376dbd54de606cb9a')  # Default value as fallback

# Create directories for saved models if they don't exist
os.makedirs("models", exist_ok=True)

print("Loading and preprocessing data...")
# Load the labeled CSV dataset
df = pd.read_csv("data1.csv", encoding='ISO-8859-1')

# Ensure column names are lowercase for consistency
if 'text' not in df.columns:
    df.columns = [col.lower() for col in df.columns]

# Clean the dataset - ensure no missing values
df = df.dropna(subset=['label', 'class'])

# Ensure all labels are integers
df['class'] = df['class'].astype(int)

print("Training BERT model...")
# Tokenization using BERT tokenizer
tokenizer = BertTokenizer.from_pretrained('bert-base-uncased')

def tokenize(example):
    # Check if text field exists and is not empty
    if "text" in example and example["text"] is not None and example["text"] != "":
        return tokenizer(example["text"], truncation=True, padding="max_length")
    else:
        # Return empty tokenization for missing text
        return tokenizer("[PAD]", truncation=True, padding="max_length")

# Convert to Hugging Face Dataset format
dataset = Dataset.from_pandas(df)
tokenized_dataset = dataset.map(tokenize)

# Rename the 'class' column to 'label' for compatibility with HuggingFace Trainer


# Load BERT model for multi-class classification (3 classes: 0 = No Risk, 1 = Suicide, 2 = Depression)
model = BertForSequenceClassification.from_pretrained("bert-base-uncased", num_labels=3)

# Training arguments
training_args = TrainingArguments(
    output_dir="./models/bert_checkpoints",
    num_train_epochs=3,
    per_device_train_batch_size=8,
    evaluation_strategy="no",
    save_strategy="no",
    logging_dir="./logs",
    learning_rate=2e-5,
    weight_decay=0.01,
)

# Trainer
trainer = Trainer(
    model=model,
    args=training_args,
    train_dataset=tokenized_dataset
)

# Train the model
print("Starting BERT training (this may take a while)...")
trainer.train()

# Save the BERT model and tokenizer
print("Saving BERT model...")
model_save_path = "./models/bert_model"
model.save_pretrained(model_save_path)
tokenizer.save_pretrained(model_save_path)

print("Training TF-IDF and RandomForest models...")
# Create example data for TF-IDF and RandomForest training
# This is a simplified version - you may want to use your actual data
example_data = pd.DataFrame({
    'Search_Query': df['text'].tolist(),
    'Time_Spent': [10] * len(df),  # Placeholder values
})

# Initialize VADER Sentiment Analyzer
analyzer = SentimentIntensityAnalyzer()
example_data['Sentiment_Score'] = example_data['Search_Query'].apply(lambda x: analyzer.polarity_scores(x)['compound'])

# Save VADER analyzer
joblib.dump(analyzer, './models/vader_analyzer.joblib')

# Keyword Extraction (TF-IDF)
vectorizer = TfidfVectorizer(stop_words='english', max_features=100)
X_text = vectorizer.fit_transform(example_data['Search_Query']).toarray()

# Save vectorizer
joblib.dump(vectorizer, './models/vectorizer.joblib')

# BERT Prediction Score for multi-class
bert_classifier = pipeline("text-classification", model=model, tokenizer=tokenizer)

def extract_bert_probabilities(text):
    preds = bert_classifier(text)
    probs = [0, 0, 0]
    for p in preds:
        label_idx = int(p['label'].split('_')[-1])
        probs[label_idx] = p['score']
    return probs

# Add BERT probabilities
example_data['BERT_Probs'] = example_data['Search_Query'].apply(extract_bert_probabilities)

# Prepare features for RandomForest
X_features = []
for i, row in example_data.iterrows():
    bert_probs = row['BERT_Probs']
    features = np.concatenate((
        X_text[i],
        [row['Time_Spent'], row['Sentiment_Score']],
        bert_probs
    ))
    X_features.append(features)

X_features = np.array(X_features)

# Use the original labels from your dataset
y = df['class'].values

# Split Data
X_train, X_test, y_train, y_test = train_test_split(X_features, y, test_size=0.2, random_state=42)

# Train RandomForest Model
rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
rf_model.fit(X_train, y_train)

# Evaluate the model
y_pred = rf_model.predict(X_test)
print("RandomForest Accuracy:", accuracy_score(y_test, y_pred))
print("Classification Report:\n", classification_report(y_test, y_pred))

# Save RandomForest model
joblib.dump(rf_model, './models/rf_model.joblib')

print("All models trained and saved successfully!")
print("You can now use predict.py to make predictions using these models.")

