accelerate==1.6.0
aiohappyeyeballs==2.4.4
aiohttp==3.11.11
aiohttp-retry==2.8.3
aiosignal==1.3.2
altair==5.5.0
annotated-types==0.7.0
ansi2html==1.9.2
asgiref==3.8.1
asttokens==3.0.0
attrs==24.2.0
bcrypt==4.2.1
beautifulsoup4==4.12.2
blinker==1.9.0
CacheControl==0.14.2
cachetools==5.5.2
certifi==2024.2.2
cffi==1.17.1
charset-normalizer==3.3.2
click==8.1.7
colorama==0.4.6
coloredlogs==15.0.1
comm==0.2.2
contourpy==1.2.1
cryptography==44.0.2
cycler==0.12.1
dash==3.0.2
dash-bootstrap-components==2.0.0
dash-core-components==2.0.0
dash-html-components==2.0.0
dash-table==5.0.0
datasets==3.5.0
debugpy==1.8.13
decorator==5.2.1
dill==0.3.8
distlib==0.3.8
Django==5.0.7
dnspython==2.7.0
docker-pycreds==0.4.0
executing==2.2.0
filelock==3.13.2
firebase-admin==6.2.0
Flask==3.0.0
flask-cors==5.0.1
Flask-PyMongo==3.0.1
flatbuffers==25.1.21
fonttools==4.53.1
frozenlist==1.5.0
fsspec==2024.12.0
generativeai==0.0.1
gitdb==4.0.12
GitPython==3.1.44
googleapis-common-protos==1.69.1
google-ai-generativelanguage==0.6.15
google-api-core==2.24.2
google-api-python-client==2.163.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-cloud-core==2.4.3
google-cloud-firestore==2.20.1
google-cloud-storage==3.1.0
google-crc32c==1.7.1
google-generativeai==0.8.4
google-resumable-media==2.7.2
grpcio==1.71.0
grpcio-status==1.71.0
h11==0.14.0
httplib2==0.22.0
huggingface-hub==0.29.2
humanfriendly==10.0
idna==3.6
importlib_metadata==8.6.1
install==1.3.5
ipykernel==6.29.5
ipython==9.0.2
ipython_pygments_lexers==1.1.1
itsdangerous==2.2.0
jedi==0.19.2
Jinja2==3.1.4
joblib==1.3.2
jsonschema==4.23.0
jsonschema-specifications==2024.10.1
jupyter_client==8.6.3
jupyter_core==5.7.2
kiwisolver==1.4.5
lxml==5.1.1
MarkupSafe==3.0.2
matplotlib==3.9.1
matplotlib-inline==0.1.7
mpmath==1.3.0
msgpack==1.1.0
multidict==6.1.0
multiprocess==0.70.16
mysql==0.0.3
mysqlclient==2.2.4
mysql-connector==2.2.9
mysql-connector-python==9.0.0
narwhals==1.30.0
nest-asyncio==1.6.0
networkx==3.4.2
nltk==3.8.1
numpy==1.26.2
onnxruntime==1.20.1
opencv-contrib-python==*********
opencv-python==*********
opencv-python-headless==*********
outcome==1.3.0.post0
packaging==24.1
pandas==2.1.3
parso==0.8.4
pillow==10.4.0
pip==24.1.2
pipenv==2023.12.1
platformdirs==4.2.0
plotly==5.18.0
prompt_toolkit==3.0.50
propcache==0.2.1
protobuf==5.29.3
proto-plus==1.26.1
psutil==6.1.1
pure_eval==0.2.3
pyarrow==19.0.1
pyasn1==0.6.1
pyasn1_modules==0.4.1
pycparser==2.22
pydantic==2.10.6
pydantic_core==2.27.2
pydeck==0.9.1
Pygments==2.19.1
PyJWT==2.10.1
pymongo==4.11.3
PyMySQL==1.1.1
pyparsing==3.1.2
pyreadline3==3.5.4
PySocks==1.7.1
python-dateutil==2.9.0.post0
python-dotenv==1.0.0
pytz==2024.1
pywin32==310
PyYAML==6.0.2
pyzmq==26.3.0
py-cpuinfo==9.0.0
referencing==0.36.2
regex==2023.12.25
requests==2.31.0
retrying==1.3.4
rpds-py==0.23.1
rsa==4.9
safetensors==0.5.3
scikit-learn==1.6.1
scipy==1.15.1
seaborn==0.13.2
selenium==4.15.2
sentry-sdk==2.26.0
setproctitle==1.3.5
setuptools==69.2.0
six==1.16.0
smmap==5.0.2
sniffio==1.3.1
sortedcontainers==2.4.0
soupsieve==2.5
sqlparse==0.4.4
stack-data==0.6.3
streamlit==1.43.1
sympy==1.13.1
tenacity==9.0.0
textblob==0.18.0.post0
threadpoolctl==3.6.0
tokenizers==0.21.1
toml==0.10.2
torch==2.5.1
torchvision==0.20.1
tornado==6.4.2
tqdm==4.67.1
traitlets==5.14.3
transformers==4.50.3
trio==0.27.0
trio-websocket==0.11.1
twilio==9.4.3
typing_extensions==4.12.2
tzdata==2024.1
ultralytics==8.3.65
ultralytics-thop==2.0.14
uritemplate==4.1.1
urllib3==2.2.1
vaderSentiment==3.3.2
virtualenv==20.25.1
wandb==0.19.9
watchdog==6.0.0
wcwidth==0.2.13
webdriver-manager==4.0.1
websocket-client==1.8.0
Werkzeug==3.0.6
wsproto==1.2.0
xxhash==3.5.0
yarl==1.18.3
zipp==3.21.0
plotly==5.24.1
pip==24.0
