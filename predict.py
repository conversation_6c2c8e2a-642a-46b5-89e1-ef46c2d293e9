import os
import joblib
import numpy as np
import pandas as pd
import pymongo
import re
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
from transformers import pipeline
from collections import Counter
from datetime import datetime

# Load trained components
vectorizer = joblib.load('./models/vectorizer.joblib')
scaler = joblib.load('./models/scaler.joblib')
rf_model = joblib.load('./models/rf_model.joblib')
label_mapping = joblib.load('./models/label_mapping.joblib')
reverse_label_mapping = {v: k for k, v in label_mapping.items()}

analyzer = SentimentIntensityAnalyzer()
bert_classifier = pipeline("text-classification", model="./models/bert_model", tokenizer="./models/bert_model", truncation=True)

# MongoDB Connection
mongo_url = "mongodb+srv://karthikroshan3456:<EMAIL>/mydatabase?retryWrites=true&w=majority"
client = pymongo.MongoClient(mongo_url)
db = client.get_database("SmartMindWatch")
collection = db.get_collection("searchhistories")
prediction_collection = db.get_collection("prediction")
individual_collection = db.get_collection("individual_prediction")
    
# Fetch all documents
documents = list(collection.find())

# Prepare data
data = []
for doc in documents:
    query = doc.get("query", "")
    time_str = doc.get("totalTimeSpent", "0m 0s")

    # Convert time to seconds
    match = re.match(r"(?:(\d+)m)?\s*(\d+)s", time_str)
    minutes = int(match.group(1)) if match and match.group(1) else 0
    seconds = int(match.group(2)) if match else 0
    total_seconds = minutes * 60 + seconds

    data.append({
        "userId": doc.get("userId"),
        "userName": doc.get("name"),
        "query": query,
        "time_spent": total_seconds,
        "dateAndTime": doc.get("dateAndTime")

    })

df = pd.DataFrame(data)

# Sentiment scores
df['sentiment'] = df['query'].apply(lambda x: analyzer.polarity_scores(x)['compound'])


# TF-IDF
X_text = vectorizer.transform(df['query']).toarray()

# Rename and scale
df.rename(columns={'sentiment': 'Sentiment_Score', 'time_spent': 'Time_Spent'}, inplace=True)
scaled = scaler.transform(df[['Time_Spent', 'Sentiment_Score']])
df['time_scaled'] = scaled[:, 0]
df['sentiment_scaled'] = scaled[:, 1]

# BERT probabilities
def get_bert_probs(text):
    try:
        preds = bert_classifier(text)
        probs = [0] * 5
        for p in preds:
            label_idx = int(p['label'].split('_')[-1])
            probs[label_idx] = p['score']
        return probs
    except:
        return [0] * 5

df['bert_probs'] = df['query'].apply(get_bert_probs)

# Combine features
X_features = []
for i, row in df.iterrows():
    features = np.concatenate((
        X_text[i],
        [row['time_scaled'], row['sentiment_scaled']],
        row['bert_probs']
    ))
    X_features.append(features)

X = np.array(X_features)

# Predict
label_map = {
     0:"No_Risk",
     1:"Depression",
     2:"Suicide",
     3:"Isolation",
     4:"Anxiety"
}
predictions = rf_model.predict(X)
df['predicted_label'] = predictions
df['predicted_risk'] = df['predicted_label'].map(label_map)
now = datetime.now()
formated_date_time = now.strftime("date:%d-%m-%Y," "time:%H:%M:%S")

# User-Level Mental Health Summary
print("🧠 Overall Mental Health Risk Predictions (per user):")
print("======================================================")
for user_id, group in df.groupby('userId'):
    risks = group['predicted_label'].tolist()
    risk_counts = Counter(risks)
    most_common_risk_label, _ = risk_counts.most_common(1)[0]
    if most_common_risk_label == 0:
        other_risks = {k:v for k,v in risk_counts.items() if k!=0}
        
        if any(v>0 for v in other_risks.values()):
            most_common_risk_label = max(other_risks.items(), key=lambda item: item[1])[0]
            user_risk = label_map[most_common_risk_label]
        else:
            user_risk = "No_Risk"
    else:
        user_risk = label_map[most_common_risk_label]
    

    # Build risk stats per category
    user_summary = {
        "User_id": user_id,
        "User_Name": df.loc[df['userId'] == user_id, 'userName'].iloc[0],
        "prediction": user_risk,
        "Suicide": risk_counts.get(2, 0),
        "Anxiety": risk_counts.get(4, 0),
        "Isolation": risk_counts.get(3, 0),
        "Depression": risk_counts.get(1, 0),
        "No_Risk": risk_counts.get(0, 0),
        "Total_search": len(group),
        "Predicted_date": formated_date_time
    } 
    print(user_summary)
    # Insert into prediction collection
    prediction_collection.insert_one(user_summary)

    #print(f"👤 UserID: {user_id}")
    #print(f"   📝 Total Queries: {len(group)}")
    #print(f"   🔍 Risk Distribution: {dict((label_map[k], v) for k, v in risk_counts.items())}")
    #print(f"   🔮 Predicted Mental Health Status: **{user_risk}**")
    #print("------------------------------------------------------")

# Store individual query-level predictions
print("\n📊 Storing Individual Query-Level Predictions to MongoDB:")
print("==========================================================")

for _, row in df.iterrows():
    individual_collection.insert_one({
        "User_Id": row['userId'],
        "User_Name": df.loc[df['userId'] == user_id, 'userName'].iloc[0],
        "Query": row['query'],
        "Time_spent": row['Time_Spent'],
        "Sentiment_Score": row['Sentiment_Score'],
        "Time_Scaled": row['time_scaled'],
        "Sentiment_Scaled": row['sentiment_scaled'],
        "Predicted_Label": int(row['predicted_label']),
        "Predicted_Risk": row['predicted_risk'],
        "Predicted_Date": formated_date_time,
        "Searched_Date": row['dateAndTime']
        
    })

    #print(f"✅ Stored for UserID: {row['userId']} | Query: {row['query']}")
