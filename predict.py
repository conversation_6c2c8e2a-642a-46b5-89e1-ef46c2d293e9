# predict.py - Load trained models and make predictions

import pandas as pd
import numpy as np
import re
import joblib
import os
import dotenv
from pymongo import MongoClient
from transformers import BertTokenizer, BertForSequenceClassification, pipeline

# Load environment variables from .env file if it exists
dotenv.load_dotenv()

# Store API key in environment variable
API_KEY = os.getenv('API_KEY', '45430e6aea0b8a83f2d65fa376dbd54de606cb9a')  # Default value as fallback

# Check if models exist
if not os.path.exists('./models/rf_model.joblib'):
    print("Error: Models not found. Please run train.py first to train the models.")
    exit(1)

print("Loading models...")
# Load models
rf_model = joblib.load('./models/rf_model.joblib')
vectorizer = joblib.load('./models/vectorizer.joblib')
analyzer = joblib.load('./models/vader_analyzer.joblib')

# Load BERT
tokenizer = BertTokenizer.from_pretrained("./models/bert_model")
bert_model = BertForSequenceClassification.from_pretrained("./models/bert_model")
bert_classifier = pipeline("text-classification", model=bert_model, tokenizer=tokenizer)

print("Models loaded successfully!")

# MongoDB connection
try:
    # Use API_KEY for authentication if needed
    mongo_uri = f"mongodb+srv://karthikroshan3456:{API_KEY}@cluster0.u3lsm.mongodb.net/mydatabase?retryWrites=true&w=majority"
    client = MongoClient(mongo_uri)
    db = client["SmartMindWatch"]
    collection = db["searchhistories"]
    chrome_data = pd.DataFrame(list(collection.find({}, {"_id": 0})))
    print(f"Successfully loaded {len(chrome_data)} records from MongoDB")

    if len(chrome_data) == 0:
        print("Warning: No data found in MongoDB collection. Using sample data for demonstration.")
        # Create sample data for demonstration
        chrome_data = pd.DataFrame({
            'query': ["I want to end my life", "How to fight depression", "No one understands me", "Therapy near me"],
            'totalTimeSpent': ["10m 15s", "8m 0s", "10m 30s", "6m 45s"]
        })
except Exception as e:
    print(f"Error connecting to MongoDB: {e}")
    print("Using sample data for demonstration.")
    # Create sample data for demonstration
    chrome_data = pd.DataFrame({
        'query': ["I want to end my life", "How to fight depression", "No one understands me", "Therapy near me"],
        'totalTimeSpent': ["10m 15s", "8m 0s", "10m 30s", "6m 45s"]
    })

# Parse time string
def parse_time(time_str):
    match = re.match(r"(\d+)m (\d+)s", time_str)
    if match:
        return int(match.group(1)) * 60 + int(match.group(2))
    return 0

chrome_data['Time_Spent'] = chrome_data['totalTimeSpent'].apply(parse_time)

# Extract BERT probabilities
def extract_bert_probabilities(text):
    preds = bert_classifier(text)
    probs = [0, 0, 0]
    for p in preds:
        label_idx = int(p['label'].split('_')[-1])
        probs[label_idx] = p['score']
    return probs

# Prediction function
def predict_risk(text, time_spent):
    vader_score = analyzer.polarity_scores(text)['compound']
    tfidf_vector = vectorizer.transform([text]).toarray()
    bert_probs = extract_bert_probabilities(text)
    features = np.column_stack((tfidf_vector, [[time_spent, vader_score] + bert_probs]))
    return rf_model.predict(features)[0]

chrome_data['Predicted_Risk'] = chrome_data.apply(lambda row: predict_risk(row['query'], row['Time_Spent']), axis=1)
label_map = {0: "No Risk", 1: "Suicide", 2: "Depression"}
chrome_data['Risk_Label'] = chrome_data['Predicted_Risk'].map(label_map)

# Final Output
print(chrome_data[['query', 'Time_Spent', 'Risk_Label']])
