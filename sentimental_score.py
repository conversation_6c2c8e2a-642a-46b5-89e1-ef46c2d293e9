import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pymongo import MongoClient
from collections import defaultdict
import warnings

# Filter out warnings
warnings.filterwarnings("ignore")

# MongoDB connection string
mongo_url = "mongodb+srv://karthikroshan3456:<EMAIL>/mydatabase?retryWrites=true&w=majority"

def connect_to_mongodb():
    """Connect to MongoDB and return client and collections"""
    try:
        client = MongoClient(mongo_url)
        db = client["SmartMindWatch"]
        individual_prediction_collection = db["individual_prediction"]
        sentiment_score_collection = db["sentimental_Score Based on date"]
        print("Successfully connected to MongoDB")
        return client, individual_prediction_collection, sentiment_score_collection
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        return None, None, None

def get_all_prediction_data(individual_prediction_collection):
    """Get all data from individual_prediction collection"""
    try:
        cursor = individual_prediction_collection.find({})
        data = list(cursor)

        if not data:
            print("No data found in individual_prediction collection")
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(data)
        print(f"Found {len(df)} records in individual_prediction collection")
        return df

    except Exception as e:
        print(f"Error fetching data from MongoDB: {e}")
        return pd.DataFrame()

def process_sentiment_by_date(df):
    """Process sentiment data grouped by Searched_Date"""
    if df.empty:
        print("No data to process")
        return {}

    # Check if required columns exist
    if 'Searched_Date' not in df.columns:
        print("Error: 'Searched_Date' column not found in data")
        return {}

    if 'Sentiment_Scaled' not in df.columns:
        print("Error: 'Sentiment_Scaled' column not found in data")
        return {}

    # Group by Searched_Date and sum Sentiment_Scaled
    date_sentiment_dict = {}

    # Convert Searched_Date to string format if it's not already
    df['Searched_Date'] = df['Searched_Date'].astype(str)

    # Group by date and calculate metrics
    grouped = df.groupby('Searched_Date')

    for date, group in grouped:
        # Calculate total sentiment scaled for this date
        total_sentiment_scaled = group['Sentiment_Scaled'].sum()
        count_records = len(group)
        average_sentiment_scaled = group['Sentiment_Scaled'].mean()

        date_sentiment_dict[date] = {
            'date': date,
            'total_sentiment_scaled': float(total_sentiment_scaled),
            'count_records': int(count_records),
            'average_sentiment_scaled': float(average_sentiment_scaled),
            'min_sentiment_scaled': float(group['Sentiment_Scaled'].min()),
            'max_sentiment_scaled': float(group['Sentiment_Scaled'].max()),
            'timestamp': datetime.now()
        }

        print(f"Date: {date} - Total Sentiment Scaled: {total_sentiment_scaled:.2f} (Records: {count_records})")

    return date_sentiment_dict

def save_sentiment_scores(sentiment_score_collection, date_sentiment_dict):
    """Save sentiment scores grouped by date to MongoDB"""
    if not date_sentiment_dict:
        print("No data to save")
        return False

    try:
        saved_count = 0
        updated_count = 0

        for date, metrics in date_sentiment_dict.items():
            # Check if record for this date already exists
            existing_record = sentiment_score_collection.find_one({'date': date})

            if existing_record:
                # Update existing record
                result = sentiment_score_collection.update_one(
                    {'date': date},
                    {'$set': metrics}
                )
                updated_count += 1
                print(f"Updated sentiment record for date: {date}")
            else:
                # Insert new record
                result = sentiment_score_collection.insert_one(metrics)
                saved_count += 1
                print(f"Inserted new sentiment record for date: {date}")

        print(f"\nSummary: {saved_count} new records inserted, {updated_count} records updated")
        return True

    except Exception as e:
        print(f"Error saving sentiment scores to MongoDB: {e}")
        return False

def get_sentiment_summary(sentiment_score_collection):
    """Get summary of all sentiment scores by date"""
    try:
        cursor = sentiment_score_collection.find({}).sort("date", -1)
        records = list(cursor)

        if records:
            print(f"\n=== Sentiment Score Summary ({len(records)} dates) ===")
            total_sentiment = 0
            total_records = 0

            for record in records:
                print(f"Date: {record['date']}")
                print(f"  Total Sentiment Scaled: {record['total_sentiment_scaled']:.2f}")
                print(f"  Average Sentiment Scaled: {record['average_sentiment_scaled']:.2f}")
                print(f"  Records Count: {record['count_records']}")
                print(f"  Range: {record['min_sentiment_scaled']:.2f} - {record['max_sentiment_scaled']:.2f}")
                print()

                total_sentiment += record['total_sentiment_scaled']
                total_records += record['count_records']

            if total_records > 0:
                overall_average = total_sentiment / total_records
                print(f"=== Overall Statistics ===")
                print(f"Total Records Processed: {total_records}")
                print(f"Total Sentiment Scaled: {total_sentiment:.2f}")
                print(f"Overall Average Sentiment: {overall_average:.2f}")
        else:
            print("No sentiment score records found")

    except Exception as e:
        print(f"Error fetching sentiment summary: {e}")

def main():
    """Main function to process sentiment scores by date"""
    print("=== Sentiment Score Processor by Date ===")

    # Connect to MongoDB
    client, individual_prediction_collection, sentiment_score_collection = connect_to_mongodb()
    if not client:
        return

    try:
        # Get all data from individual_prediction collection
        prediction_data = get_all_prediction_data(individual_prediction_collection)

        if prediction_data.empty:
            print("No data available to process")
            return

        # Process sentiment data by date
        date_sentiment_dict = process_sentiment_by_date(prediction_data)

        if date_sentiment_dict:
            # Save to MongoDB
            success = save_sentiment_scores(sentiment_score_collection, date_sentiment_dict)

            if success:
                print("\n✅ Sentiment scores successfully processed and saved!")

                # Show summary
                get_sentiment_summary(sentiment_score_collection)
            else:
                print("❌ Failed to save sentiment scores")
        else:
            print("❌ Failed to process sentiment data")

    except Exception as e:
        print(f"Error in main function: {e}")

    finally:
        # Close MongoDB connection
        if client:
            client.close()
            print("\nMongoDB connection closed")

if __name__ == "__main__":
    main()