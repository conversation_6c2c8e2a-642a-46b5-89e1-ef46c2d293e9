import pandas as pd
from datetime import datetime
from pymongo import MongoClient
import warnings

# Filter out warnings
warnings.filterwarnings("ignore")

# MongoDB connection string
mongo_url = "mongodb+srv://karthikroshan3456:<EMAIL>/mydatabase?retryWrites=true&w=majority"

def connect_to_mongodb():
    """Connect to MongoDB and return client and collections"""
    try:
        client = MongoClient(mongo_url)
        db = client["SmartMindWatch"]
        individual_prediction_collection = db["individual_prediction"]
        searchhistories_collection = db["searchhistories"]
        sentiment_score_collection = db["sentimental_Score Based on date"]
        print("Successfully connected to MongoDB")
        return client, individual_prediction_collection, searchhistories_collection, sentiment_score_collection
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        return None, None, None, None

def get_all_prediction_data(individual_prediction_collection):
    """Get all data from individual_prediction collection"""
    try:
        cursor = individual_prediction_collection.find({})
        data = list(cursor)

        if not data:
            print("No data found in individual_prediction collection")
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(data)
        print(f"Found {len(df)} records in individual_prediction collection")
        return df

    except Exception as e:
        print(f"Error fetching data from MongoDB: {e}")
        return pd.DataFrame()

def get_searchhistories_data(searchhistories_collection):
    """Get user_id and query data from searchhistories collection"""
    try:
        cursor = searchhistories_collection.find({}, {"user_id": 1, "query": 1, "_id": 0})
        data = list(cursor)

        if not data:
            print("No data found in searchhistories collection")
            return pd.DataFrame()

        # Convert to DataFrame
        df = pd.DataFrame(data)
        print(f"Found {len(df)} records in searchhistories collection")
        return df

    except Exception as e:
        print(f"Error fetching searchhistories data from MongoDB: {e}")
        return pd.DataFrame()

def merge_prediction_with_user_data(prediction_df, searchhistories_df):
    """Merge prediction data with user_id from searchhistories"""
    if prediction_df.empty or searchhistories_df.empty:
        print("Cannot merge: one or both dataframes are empty")
        return pd.DataFrame()

    try:
        # Merge on 'query' field to get user_id
        merged_df = prediction_df.merge(
            searchhistories_df,
            on='query',
            how='left'
        )

        # Remove rows where user_id is null (no match found)
        merged_df = merged_df.dropna(subset=['user_id'])

        print(f"Successfully merged data: {len(merged_df)} records with user_id")
        return merged_df

    except Exception as e:
        print(f"Error merging data: {e}")
        return pd.DataFrame()

def process_sentiment_by_date(df):
    """Process sentiment data grouped by Searched_Date and user_id"""
    if df.empty:
        print("No data to process")
        return {}

    # Check if required columns exist
    if 'Searched_Date' not in df.columns:
        print("Error: 'Searched_Date' column not found in data")
        return {}

    if 'Sentiment_Scaled' not in df.columns:
        print("Error: 'Sentiment_Scaled' column not found in data")
        return {}

    if 'user_id' not in df.columns:
        print("Error: 'user_id' column not found in data")
        return {}

    # Extract only date part (remove time) from Searched_Date
    def extract_date_only(date_value):
        try:
            if pd.isna(date_value):
                return None
            # Convert to string first
            date_str = str(date_value)
            # If it's a datetime string, extract only the date part
            if ' ' in date_str:
                return date_str.split(' ')[0]  # Take only date part before space
            elif 'T' in date_str:
                return date_str.split('T')[0]  # Take only date part before T
            else:
                return date_str  # Already just date
        except:
            return str(date_value)

    # Extract date only from Searched_Date
    df['Date_Only'] = df['Searched_Date'].apply(extract_date_only)

    # Remove any null dates
    df = df.dropna(subset=['Date_Only'])

    # Group by date and user_id
    date_user_sentiment_dict = {}
    grouped = df.groupby(['Date_Only', 'user_id'])

    for (date, user_id), group in grouped:
        # Calculate total sentiment scaled for this date and user
        total_sentiment_scaled = group['Sentiment_Scaled'].sum()

        # Create unique key for date-user combination
        key = f"{date}_{user_id}"

        date_user_sentiment_dict[key] = {
            'date': date,
            'user_id': str(user_id),
            'total_sentiment_scaled': float(total_sentiment_scaled)
        }

        print(f"Date: {date}, User ID: {user_id} - Total Sentiment Scaled: {total_sentiment_scaled:.2f}")

    return date_user_sentiment_dict

def save_sentiment_scores(sentiment_score_collection, date_user_sentiment_dict):
    """Save sentiment scores grouped by date and user_id to MongoDB"""
    if not date_user_sentiment_dict:
        print("No data to save")
        return False

    try:
        saved_count = 0
        updated_count = 0

        for key, metrics in date_user_sentiment_dict.items():
            # Check if record for this date and user_id already exists
            existing_record = sentiment_score_collection.find_one({
                'date': metrics['date'],
                'user_id': metrics['user_id']
            })

            if existing_record:
                # Update existing record
                sentiment_score_collection.update_one(
                    {'date': metrics['date'], 'user_id': metrics['user_id']},
                    {'$set': metrics}
                )
                updated_count += 1
                print(f"Updated sentiment record for date: {metrics['date']}, user: {metrics['user_id']}")
            else:
                # Insert new record
                sentiment_score_collection.insert_one(metrics)
                saved_count += 1
                print(f"Inserted new sentiment record for date: {metrics['date']}, user: {metrics['user_id']}")

        print(f"\nSummary: {saved_count} new records inserted, {updated_count} records updated")
        return True

    except Exception as e:
        print(f"Error saving sentiment scores to MongoDB: {e}")
        return False

def get_sentiment_summary(sentiment_score_collection):
    """Get summary of all sentiment scores by date"""
    try:
        cursor = sentiment_score_collection.find({}).sort("date", -1)
        records = list(cursor)

        if records:
            print(f"\n=== Sentiment Score Summary ({len(records)} records) ===")
            total_sentiment = 0

            for record in records:
                print(f"Date: {record['date']}, User ID: {record['user_id']} - Total Sentiment Scaled: {record['total_sentiment_scaled']:.2f}")
                total_sentiment += record['total_sentiment_scaled']

            print(f"\n=== Overall Total Sentiment: {total_sentiment:.2f} ===")
        else:
            print("No sentiment score records found")

    except Exception as e:
        print(f"Error fetching sentiment summary: {e}")

def main():
    """Main function to process sentiment scores by date"""
    print("=== Sentiment Score Processor by Date ===")

    # Connect to MongoDB
    client, individual_prediction_collection, searchhistories_collection, sentiment_score_collection = connect_to_mongodb()
    if not client:
        return

    try:
        # Get all data from individual_prediction collection
        prediction_data = get_all_prediction_data(individual_prediction_collection)

        if prediction_data.empty:
            print("No data available to process")
            return

        # Get user_id data from searchhistories collection
        searchhistories_data = get_searchhistories_data(searchhistories_collection)

        if searchhistories_data.empty:
            print("No searchhistories data available")
            return

        # Merge prediction data with user_id
        merged_data = merge_prediction_with_user_data(prediction_data, searchhistories_data)

        if merged_data.empty:
            print("No merged data available to process")
            return

        # Process sentiment data by date and user
        date_user_sentiment_dict = process_sentiment_by_date(merged_data)

        if date_user_sentiment_dict:
            # Save to MongoDB
            success = save_sentiment_scores(sentiment_score_collection, date_user_sentiment_dict)

            if success:
                print("\n✅ Sentiment scores successfully processed and saved!")

                # Show summary
                get_sentiment_summary(sentiment_score_collection)
            else:
                print("❌ Failed to save sentiment scores")
        else:
            print("❌ Failed to process sentiment data")

    except Exception as e:
        print(f"Error in main function: {e}")

    finally:
        # Close MongoDB connection
        if client:
            client.close()
            print("\nMongoDB connection closed")

if __name__ == "__main__":
    main()