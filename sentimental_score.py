import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from pymongo import MongoClient
from vaderSentiment.vaderSentiment import SentimentIntensityAnalyzer
import warnings

# Filter out warnings
warnings.filterwarnings("ignore")

# MongoDB connection
mongo_url = "mongodb+srv://karthikroshan3456:<EMAIL>/mydatabase?retryWrites=true&w=majority"

def connect_to_mongodb():
    """Connect to MongoDB and return client and collections"""
    try:
        client = MongoClient(mongo_url)
        db = client["SmartMindWatch"]
        search_collection = db["searchhistories"]
        sentiment_collection = db["daily_sentiment_scores"]
        print("Successfully connected to MongoDB")
        return client, search_collection, sentiment_collection
    except Exception as e:
        print(f"Error connecting to MongoDB: {e}")
        return None, None, None

def calculate_sentiment_score(text):
    """Calculate sentiment score using VADER"""
    analyzer = SentimentIntensityAnalyzer()
    scores = analyzer.polarity_scores(text)
    return scores['compound']  # Returns compound score between -1 and 1

def scale_sentiment_score(compound_score):
    """Scale sentiment score from [-1, 1] to [0, 100]"""
    # Convert from [-1, 1] to [0, 100] scale
    scaled_score = ((compound_score + 1) / 2) * 100
    return round(scaled_score, 2)

def parse_time_spent(time_str):
    """Parse time string like '10m 15s' to seconds"""
    import re
    if not time_str or pd.isna(time_str):
        return 0
    
    match = re.match(r"(\d+)m (\d+)s", str(time_str))
    if match:
        minutes = int(match.group(1))
        seconds = int(match.group(2))
        return minutes * 60 + seconds
    return 0

def get_daily_search_data(search_collection, target_date=None):
    """Get search data for a specific date"""
    if target_date is None:
        target_date = datetime.now().date()
    
    # Convert date to datetime range for MongoDB query
    start_date = datetime.combine(target_date, datetime.min.time())
    end_date = start_date + timedelta(days=1)
    
    try:
        # Query MongoDB for data from the target date
        query = {
            "timestamp": {
                "$gte": start_date,
                "$lt": end_date
            }
        }
        
        cursor = search_collection.find(query)
        data = list(cursor)
        
        if not data:
            print(f"No search data found for {target_date}")
            return pd.DataFrame()
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        print(f"Found {len(df)} search records for {target_date}")
        return df
        
    except Exception as e:
        print(f"Error fetching data from MongoDB: {e}")
        return pd.DataFrame()

def calculate_daily_sentiment(df):
    """Calculate daily sentiment metrics from search data"""
    if df.empty:
        return None
    
    # Calculate sentiment scores for each query
    df['sentiment_score'] = df['query'].apply(calculate_sentiment_score)
    df['sentiment_scaled'] = df['sentiment_score'].apply(scale_sentiment_score)
    
    # Parse time spent
    df['time_spent_seconds'] = df['totalTimeSpent'].apply(parse_time_spent)
    
    # Calculate daily metrics
    daily_metrics = {
        'date': datetime.now().date().isoformat(),
        'timestamp': datetime.now(),
        'total_searches': len(df),
        'average_sentiment_score': float(df['sentiment_score'].mean()),
        'average_sentiment_scaled': float(df['sentiment_scaled'].mean()),
        'min_sentiment_scaled': float(df['sentiment_scaled'].min()),
        'max_sentiment_scaled': float(df['sentiment_scaled'].max()),
        'total_time_spent_seconds': int(df['time_spent_seconds'].sum()),
        'total_time_spent_minutes': round(df['time_spent_seconds'].sum() / 60, 2),
        'negative_searches': int((df['sentiment_score'] < -0.1).sum()),
        'neutral_searches': int((df['sentiment_score'] >= -0.1) & (df['sentiment_score'] <= 0.1)).sum(),
        'positive_searches': int((df['sentiment_score'] > 0.1).sum()),
        'most_negative_query': df.loc[df['sentiment_score'].idxmin(), 'query'] if not df.empty else "",
        'most_positive_query': df.loc[df['sentiment_score'].idxmax(), 'query'] if not df.empty else "",
        'queries_analyzed': df[['query', 'sentiment_score', 'sentiment_scaled', 'time_spent_seconds']].to_dict('records')
    }
    
    return daily_metrics

def save_daily_sentiment(sentiment_collection, daily_metrics):
    """Save daily sentiment metrics to MongoDB"""
    try:
        # Check if data for this date already exists
        existing_record = sentiment_collection.find_one({'date': daily_metrics['date']})
        
        if existing_record:
            # Update existing record
            result = sentiment_collection.update_one(
                {'date': daily_metrics['date']},
                {'$set': daily_metrics}
            )
            print(f"Updated existing sentiment record for {daily_metrics['date']}")
        else:
            # Insert new record
            result = sentiment_collection.insert_one(daily_metrics)
            print(f"Inserted new sentiment record for {daily_metrics['date']}")
        
        return True
    except Exception as e:
        print(f"Error saving to MongoDB: {e}")
        return False

def main():
    """Main function to calculate and save daily sentiment scores"""
    print("=== Daily Sentiment Score Calculator ===")

    # Connect to MongoDB
    client, search_collection, sentiment_collection = connect_to_mongodb()
    if not client:
        return

    try:
        # Get today's search data
        today_data = get_daily_search_data(search_collection)

        if today_data.empty:
            print("No search data available for today. Creating sample data for demonstration.")
            # Create sample data for demonstration
            sample_data = pd.DataFrame({
                'query': [
                    "I feel sad today",
                    "How to be happy",
                    "Depression help",
                    "Good morning sunshine",
                    "I love my life"
                ],
                'totalTimeSpent': ["2m 30s", "1m 45s", "3m 15s", "0m 45s", "1m 20s"]
            })
            today_data = sample_data

        # Calculate daily sentiment metrics
        daily_metrics = calculate_daily_sentiment(today_data)

        if daily_metrics:
            # Save to MongoDB
            success = save_daily_sentiment(sentiment_collection, daily_metrics)

            if success:
                print("\n=== Daily Sentiment Summary ===")
                print(f"Date: {daily_metrics['date']}")
                print(f"Total Searches: {daily_metrics['total_searches']}")
                print(f"Average Sentiment (Scaled): {daily_metrics['average_sentiment_scaled']:.2f}/100")
                print(f"Sentiment Range: {daily_metrics['min_sentiment_scaled']:.2f} - {daily_metrics['max_sentiment_scaled']:.2f}")
                print(f"Total Time Spent: {daily_metrics['total_time_spent_minutes']:.2f} minutes")
                print(f"Negative Searches: {daily_metrics['negative_searches']}")
                print(f"Neutral Searches: {daily_metrics['neutral_searches']}")
                print(f"Positive Searches: {daily_metrics['positive_searches']}")

                if daily_metrics['average_sentiment_scaled'] < 40:
                    print("\n⚠️  WARNING: Low average sentiment detected. Consider providing mental health resources.")
                elif daily_metrics['average_sentiment_scaled'] > 70:
                    print("\n😊 POSITIVE: High average sentiment detected. User seems to be in good spirits!")
                else:
                    print("\n😐 NEUTRAL: Average sentiment is in the neutral range.")

        else:
            print("Failed to calculate daily metrics")

    except Exception as e:
        print(f"Error in main function: {e}")

    finally:
        # Close MongoDB connection
        if client:
            client.close()
            print("MongoDB connection closed")

def run_for_date(target_date_str):
    """Run sentiment analysis for a specific date (format: YYYY-MM-DD)"""
    try:
        target_date = datetime.strptime(target_date_str, "%Y-%m-%d").date()
        print(f"=== Daily Sentiment Score Calculator for {target_date} ===")

        # Connect to MongoDB
        client, search_collection, sentiment_collection = connect_to_mongodb()
        if not client:
            return

        try:
            # Get search data for the specific date
            date_data = get_daily_search_data(search_collection, target_date)

            if date_data.empty:
                print(f"No search data available for {target_date}")
                return

            # Calculate daily sentiment metrics
            daily_metrics = calculate_daily_sentiment(date_data)
            daily_metrics['date'] = target_date.isoformat()  # Override date

            if daily_metrics:
                # Save to MongoDB
                success = save_daily_sentiment(sentiment_collection, daily_metrics)

                if success:
                    print(f"\n=== Sentiment Summary for {target_date} ===")
                    print(f"Total Searches: {daily_metrics['total_searches']}")
                    print(f"Average Sentiment (Scaled): {daily_metrics['average_sentiment_scaled']:.2f}/100")
                    print(f"Sentiment Range: {daily_metrics['min_sentiment_scaled']:.2f} - {daily_metrics['max_sentiment_scaled']:.2f}")
                    print(f"Total Time Spent: {daily_metrics['total_time_spent_minutes']:.2f} minutes")

        finally:
            if client:
                client.close()

    except ValueError:
        print("Invalid date format. Please use YYYY-MM-DD format.")
    except Exception as e:
        print(f"Error processing date {target_date_str}: {e}")

def get_sentiment_history(days=7):
    """Get sentiment history for the last N days"""
    client, _, sentiment_collection = connect_to_mongodb()
    if not client:
        return

    try:
        # Get records from the last N days
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        query = {
            "timestamp": {
                "$gte": start_date,
                "$lte": end_date
            }
        }

        cursor = sentiment_collection.find(query).sort("date", -1)
        records = list(cursor)

        if records:
            print(f"\n=== Sentiment History (Last {days} days) ===")
            for record in records:
                print(f"Date: {record['date']}")
                print(f"  Average Sentiment: {record['average_sentiment_scaled']:.2f}/100")
                print(f"  Total Searches: {record['total_searches']}")
                print(f"  Negative/Neutral/Positive: {record['negative_searches']}/{record['neutral_searches']}/{record['positive_searches']}")
                print()
        else:
            print(f"No sentiment records found for the last {days} days")

    except Exception as e:
        print(f"Error fetching sentiment history: {e}")
    finally:
        if client:
            client.close()

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1:
        if sys.argv[1] == "history":
            days = int(sys.argv[2]) if len(sys.argv) > 2 else 7
            get_sentiment_history(days)
        elif sys.argv[1] == "date":
            if len(sys.argv) > 2:
                run_for_date(sys.argv[2])
            else:
                print("Please provide a date in YYYY-MM-DD format")
        else:
            print("Usage:")
            print("  python sentimental_score.py           # Run for today")
            print("  python sentimental_score.py date YYYY-MM-DD  # Run for specific date")
            print("  python sentimental_score.py history [days]   # Show sentiment history")
    else:
        main()
